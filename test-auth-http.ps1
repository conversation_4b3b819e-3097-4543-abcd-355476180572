# Test JWT authentication using HTTP (port 5001)

Write-Host "Testing JWT Authentication Flow on HTTP" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

# Test 1: Login to get JWT token
Write-Host "`n1. Testing login..." -ForegroundColor Green
$loginData = @{
    email = "<EMAIL>"
    password = "Test@123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5001/api/Account/Login" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "Login response:" -ForegroundColor Yellow
    $loginResponse | ConvertTo-Json -Depth 3
    
    if ($loginResponse.token) {
        $token = $loginResponse.token
        Write-Host "`nJWT Token obtained successfully!" -ForegroundColor Green
        Write-Host "Token preview: $($token.Substring(0, 50))..." -ForegroundColor Cyan
        
        # Test 2: Use token to access protected endpoint
        Write-Host "`n2. Testing protected endpoint with JWT..." -ForegroundColor Green
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        try {
            $authResponse = Invoke-RestMethod -Uri "http://localhost:5001/api/Account/TestAuth" -Method GET -Headers $headers
            Write-Host "TestAuth endpoint response:" -ForegroundColor Yellow
            $authResponse | ConvertTo-Json -Depth 3
            
            if ($authResponse.isAuthenticated) {
                Write-Host "`n✅ JWT Authentication is working correctly!" -ForegroundColor Green
                
                # Test 3: Test the GetAll endpoint that was failing
                Write-Host "`n3. Testing GetAll endpoint..." -ForegroundColor Green
                try {
                    $getAllResponse = Invoke-RestMethod -Uri "http://localhost:5001/api/Account/GetAll" -Method GET -Headers $headers
                    Write-Host "GetAll endpoint response:" -ForegroundColor Yellow
                    $getAllResponse | ConvertTo-Json -Depth 2
                    Write-Host "`n✅ GetAll endpoint is working correctly!" -ForegroundColor Green
                } catch {
                    Write-Host "`n❌ GetAll endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
                    if ($_.Exception.Response) {
                        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
                    }
                }
            } else {
                Write-Host "`n❌ Authentication failed - user not authenticated" -ForegroundColor Red
            }
        } catch {
            Write-Host "`n❌ TestAuth endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
            if ($_.Exception.Response) {
                Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "`n❌ No token received in login response" -ForegroundColor Red
    }
} catch {
    Write-Host "`n❌ Login failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n=======================================" -ForegroundColor Cyan
Write-Host "Test completed" -ForegroundColor Cyan
